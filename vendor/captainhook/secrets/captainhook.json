{"config": {"verbosity": "normal", "fail-on-first-error": false, "ansi-colors": true, "git-directory": ".git", "includes": [], "run-mode": "shell", "run-cmd": "tools/captainhook", "bootstrap": "vendor/autoload.php"}, "commit-msg": {"enabled": true, "actions": [{"action": "\\CaptainHook\\App\\Hook\\Message\\Action\\Beams", "options": {"subjectLength": 50, "bodyLineLength": 72}, "config": {"label": "Verify commit message format"}}]}, "pre-push": {"enabled": true, "actions": [{"action": "\\CaptainHook\\App\\Hook\\Branch\\Action\\BlockFixupAndSquashCommits", "options": {"protectedBranches": ["main", "master"]}, "config": {"label": "Block fixup commits from main"}}, {"action": "tools/phpstan analyse", "conditions": [{"exec": "\\CaptainHook\\App\\Hook\\Condition\\FileChanged\\OfType", "args": ["php"]}], "config": {"label": "Static code analysis"}}]}, "pre-commit": {"enabled": true, "actions": [{"action": "\\CaptainHook\\App\\Hook\\PHP\\Action\\Linting", "config": {"label": "Lint PHP files"}}, {"action": "\\CaptainHook\\App\\Hook\\File\\Action\\MaxSize", "config": {"label": "Max size check"}, "options": {"maxSize": "1M"}}, {"action": "tools/phpunit --no-coverage"}, {"action": "tools/phpcs --colors --standard=psr12 {$STAGED_FILES|of-type:php|separated-by: }", "conditions": [{"exec": "\\CaptainHook\\App\\Hook\\Condition\\FileStaged\\OfType", "args": ["php"]}]}, {"action": "\\CaptainHook\\App\\Hook\\Composer\\Action\\CheckLockFile"}]}, "post-change": {"enabled": true, "actions": [{"action": "\\CaptainHook\\App\\Hook\\Notify\\Action\\Notify"}]}}