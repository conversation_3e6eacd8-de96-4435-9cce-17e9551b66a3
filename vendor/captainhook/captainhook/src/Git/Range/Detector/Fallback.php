<?php

/**
 * This file is part of CaptainHook.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace CaptainHook\App\Git\Range\Detector;

use <PERSON><PERSON><PERSON>\App\Console\IO;
use Captain<PERSON><PERSON>\App\Git\Range;
use Captain<PERSON><PERSON>\App\Git\Range\Detecting;
use Captain<PERSON><PERSON>\App\Git\Rev;
use Captain<PERSON><PERSON>\App\Hooks;

/**
 * Fallback Detector
 *
 * If no detection strategy matches the fallback detector is used to find the right range.
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 5.15.0
 */
class Fallback implements Detecting
{
    /**
     * Returns the fallback range
     *
     * @param  \CaptainHook\App\Console\IO $io
     * @return \CaptainHook\App\Git\Range\Generic[]
     */
    public function getRanges(IO $io): array
    {
        return [
            new Range\Generic(
                new Rev\Generic($io->getArgument(Hooks::ARG_PREVIOUS_HEAD, 'HEAD@{1}')),
                new Rev\Generic('HEAD')
            )
        ];
    }
}
