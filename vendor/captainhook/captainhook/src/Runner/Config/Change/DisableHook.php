<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace CaptainHook\App\Runner\Config\Change;

use <PERSON><PERSON><PERSON>\App\Config;

/**
 * Class AddAction
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 4.2.0
 */
class DisableHook extends Hook
{
    /**
     * Apply changes to the given config
     *
     * @param  \CaptainHook\App\Config $config
     * @return void
     * @throws \Exception
     */
    public function applyTo(Config $config): void
    {
        $config->getHookConfig($this->hookToChange)->setEnabled(false);
    }
}
