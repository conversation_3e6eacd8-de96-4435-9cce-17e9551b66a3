<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace CaptainHook\App\Runner\Config\Change;

use <PERSON><PERSON><PERSON>\App\Config;
use <PERSON><PERSON><PERSON>\App\Console\IO;
use <PERSON><PERSON><PERSON>\App\Runner\Config\Change;

/**
 * Class AddAction
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 4.2.0
 */
abstract class Hook implements Change
{
    /**
     * @var \CaptainHook\App\Console\IO
     */
    protected $io;

    /**
     * Name of the hook to add the action to
     *
     * @var string
     */
    protected $hookToChange;

    /**
     * AddAction constructor
     *
     * @param \CaptainHook\App\Console\IO $io
     * @param string                      $hookToChange
     */
    public function __construct(IO $io, string $hookToChange)
    {
        $this->io           = $io;
        $this->hookToChange = $hookToChange;
    }

    /**
     * Apply changes to the given config
     *
     * @param  \CaptainHook\App\Config $config
     * @return void
     * @throws \Exception
     */
    abstract public function applyTo(Config $config): void;
}
