<?php

/**
 * This file is part of CaptainHook.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace CaptainHook\App\Runner\Action\Cli\Command\Placeholder;

/**
 * Class Env
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 5.8.0
 */
class Env extends Foundation
{
    /**
     * Return the requested ENVIRONMENT variable or a given default, returns empty string by default
     *
     * @param  array<string, mixed> $options
     * @return string
     */
    public function replacement(array $options): string
    {
        if (!isset($options['value-of'])) {
            return '';
        }

        $var     = $options['value-of'];
        $default = $options['default'] ?? '';

        return (string) ($_ENV[$var] ?? $default);
    }
}
