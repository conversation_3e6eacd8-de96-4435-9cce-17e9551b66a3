<?php

/**
 * This file is part of CaptainHook.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace CaptainHook\App\Runner\Action\Cli\Command\Placeholder;

use CaptainHook\App\Git;
use <PERSON>H<PERSON>\App\Hook\FileList;

/**
 * Changed Files Placeholder
 *
 * This placeholder only works for pre-push, post-rewrite, post-checkout and post-merge actions.
 * If it is used in a pre-push hook and multiple refs are pushed the placeholder will contain
 * all changed files for all refs.
 *
 * Usage examples:
 *  - {$CHANGED_FILES|separated-by:,}
 *  - {$CHANGED_FILES|in-dir:foo/bar}
 *  - {$CHANGED_FILES|of-type:php}
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 5.15.3
 */
class ChangedFiles extends Foundation
{
    /**
     * @param  array<string, string> $options
     * @return string
     */
    public function replacement(array $options): string
    {
        $factory  = new Git\ChangedFiles\Detector\Factory();
        $detector = $factory->getDetector($this->io, $this->repository);

        $files = $detector->getChangedFiles(['A', 'C', 'M', 'R']);

        return implode(($options['separated-by'] ?? ' '), FileList::filter($files, $options));
    }
}
