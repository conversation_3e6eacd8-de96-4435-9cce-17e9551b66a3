<?php

/**
 * This file is part of CaptainHook.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace CaptainHook\App\Hook\Condition\Branch;

use <PERSON><PERSON><PERSON>\App\Console\IO;
use <PERSON><PERSON><PERSON>\App\Hook\Condition;
use <PERSON><PERSON><PERSON><PERSON>\Git\Repository;

/**
 * On condition
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 5.20.2
 */
class On extends Name
{
    /**
     * Check is the current branch is equal to the configured one
     *
     * @param  \CaptainHook\App\Console\IO       $io
     * @param  \SebastianF<PERSON>mann\Git\Repository $repository
     * @return bool
     */
    public function isTrue(IO $io, Repository $repository): bool
    {
        return trim($repository->getInfoOperator()->getCurrentBranch()) === $this->name;
    }
}
