<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace <PERSON>Hook\App\Hook\Condition\Logic;

use Captain<PERSON><PERSON>\App\Console\IO;
use Captain<PERSON><PERSON>\App\Hook\Condition\Logic;
use <PERSON><PERSON><PERSON><PERSON>\Git\Repository;

/**
 * Connects multiple conditions with 'or'
 *
 * @package Captain<PERSON><PERSON>
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 5.7.0
 */
final class LogicOr extends Logic
{
    public function isTrue(IO $io, Repository $repository): bool
    {
        foreach ($this->conditions as $condition) {
            if (true === $condition->isTrue($io, $repository)) {
                return true;
            }
        }
        return false;
    }
}
