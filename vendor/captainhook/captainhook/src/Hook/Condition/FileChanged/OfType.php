<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace CaptainHook\App\Hook\Condition\FileChanged;

use <PERSON><PERSON><PERSON>\App\Console\IO;
use <PERSON><PERSON><PERSON>\App\Git;
use <PERSON><PERSON><PERSON>\App\Hook\Condition;
use <PERSON><PERSON><PERSON>\App\Hook\Constrained;
use <PERSON><PERSON><PERSON>\App\Hook\FileList;
use <PERSON><PERSON><PERSON>\App\Hook\Restriction;
use <PERSON><PERSON><PERSON>\App\Hooks;
use <PERSON><PERSON><PERSON><PERSON>\Git\Repository;

/**
 * Class OfType
 *
 * Example configuration:
 *
 * "action": "some-action"
 * "conditions": [
 *   {"exec": "\\CaptainHook\\App\\Hook\\Condition\\FileChanged\\OfType",
 *    "args": [
 *      "php"
 *    ]}
 * ]
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 5.0.0
 */
class OfType implements Condition, Constrained
{
    /**
     * File type to check e.g. 'php' or 'html'
     *
     * @var string
     */
    private string $suffix;

    /**
     * OfType constructor
     *
     * @param string $type
     */
    public function __construct(string $type)
    {
        $this->suffix = $type;
    }

    /**
     * Return the hook restriction information
     *
     * @return \CaptainHook\App\Hook\Restriction
     */
    public static function getRestriction(): Restriction
    {
        return Restriction::fromArray([Hooks::PRE_PUSH, Hooks::POST_CHECKOUT, Hooks::POST_MERGE, Hooks::POST_REWRITE]);
    }

    /**
     * Evaluates the condition
     *
     * @param  \CaptainHook\App\Console\IO       $io
     * @param  \SebastianFeldmann\Git\Repository $repository
     * @return bool
     */
    public function isTrue(IO $io, Repository $repository): bool
    {
        $factory  = new Git\ChangedFiles\Detector\Factory();
        $detector = $factory->getDetector($io, $repository);

        $files = $detector->getChangedFiles(['A', 'C', 'M', 'R']);
        $files = FileList::filterByType($files, ['of-type' => $this->suffix]);

        if (count($files) > 0) {
            return true;
        }
        return false;
    }
}
