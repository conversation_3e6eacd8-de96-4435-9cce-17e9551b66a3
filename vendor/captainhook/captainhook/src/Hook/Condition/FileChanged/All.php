<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace CaptainHook\App\Hook\Condition\FileChanged;

use <PERSON><PERSON><PERSON>\App\Console\IO;
use <PERSON><PERSON><PERSON>\App\Hook\Condition\FileChanged;
use <PERSON><PERSON><PERSON><PERSON>\Git\Repository;

/**
 * Class All
 *
 * The FileChange condition is applicable for `post-merge` and `post-checkout` hooks.
 * It checks if all configured files are updated within the last change set.
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 4.2.0
 */
class All extends FileChanged
{
    /**
     * Check if all the configured files were changed within the applied change set
     *
     * IMPORTANT: If no files are configured this condition is always true.
     *
     * @param  \CaptainHook\App\Console\IO       $io
     * @param  \<PERSON><PERSON><PERSON>mann\Git\Repository $repository
     * @return bool
     */
    public function isTrue(IO $io, Repository $repository): bool
    {
        return $this->allFilesInHaystack($this->filesToWatch, $this->getChangedFiles($io, $repository));
    }
}
