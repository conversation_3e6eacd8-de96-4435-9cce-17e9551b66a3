<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace CaptainHook\App\Hook\Notify\Action;

use <PERSON><PERSON><PERSON>\App\Config;
use Captain<PERSON><PERSON>\App\Console\IO;
use <PERSON><PERSON><PERSON>\App\Exception\ActionFailed;
use Captain<PERSON><PERSON>\App\Hook\Action;
use Captain<PERSON><PERSON>\App\Hook\Constrained;
use <PERSON><PERSON><PERSON>\App\Hook\Notify\Extractor;
use Captain<PERSON><PERSON>\App\Hook\Notify\Notification;
use Captain<PERSON>ook\App\Hook\Restriction;
use Captain<PERSON><PERSON>\App\Hook\Util;
use Captain<PERSON><PERSON>\App\Hooks;
use <PERSON><PERSON><PERSON><PERSON>\Cli\Processor\ProcOpen as Processor;
use <PERSON><PERSON><PERSON><PERSON>\Git\Repository;

/**
 * Class Notify
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 5.4.5
 */
class Notify implements Action, Constrained
{
    private const DEFAULT_PREFIX = 'git-notify:';

    /**
     * git-notify trigger
     *
     * @var string
     */
    private $prefix;

    /**
     * Returns a list of applicable hooks
     *
     * @return \CaptainHook\App\Hook\Restriction
     */
    public static function getRestriction(): Restriction
    {
        return Restriction::fromArray([Hooks::POST_CHECKOUT, Hooks::POST_MERGE, Hooks::POST_REWRITE]);
    }

    /**
     * Executes the action
     *
     * @param  \CaptainHook\App\Config           $config
     * @param  \CaptainHook\App\Console\IO       $io
     * @param  \SebastianFeldmann\Git\Repository $repository
     * @param  \CaptainHook\App\Config\Action    $action
     * @return void
     * @throws \Exception
     */
    public function execute(Config $config, IO $io, Repository $repository, Config\Action $action): void
    {
        $this->prefix  = $action->getOptions()->get('prefix', self::DEFAULT_PREFIX);
        $oldHash       = Util::findPreviousHead($io);
        $newHash       = $io->getArgument(Hooks::ARG_NEW_HEAD, 'HEAD');

        $logOp = $repository->getLogOperator();
        $log   = $logOp->getCommitsBetween($oldHash, $newHash);

        foreach ($log as $commit) {
            $message = $commit->getSubject() . PHP_EOL . $commit->getBody();
            if ($this->containsNotification($message)) {
                $notification = Extractor::extractNotification($message, $this->prefix);
                $this->notify($io, $notification);
            }
        }
    }

    /**
     * Checks if the commit message contains the notification prefix 'git-notify:'
     *
     * @param  string $message
     * @return bool
     */
    private function containsNotification(string $message): bool
    {
        return strpos($message, $this->prefix) !== false;
    }

    /**
     * Write the notification to the
     *
     * @param  \CaptainHook\App\Console\IO               $io
     * @param  \CaptainHook\App\Hook\Notify\Notification $notification
     * @return void
     */
    private function notify(IO $io, Notification $notification): void
    {
        $io->write(['', '', $notification->banner(), '']);
    }
}
