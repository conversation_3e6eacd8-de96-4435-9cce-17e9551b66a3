<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace CaptainHook\App\Hook\Notify\Action;

use Captain<PERSON><PERSON>\App\Config;
use <PERSON><PERSON><PERSON>\App\Console\IO;
use <PERSON><PERSON><PERSON>\App\Exception\ActionFailed;
use Captain<PERSON><PERSON>\App\Hook\Action;
use Captain<PERSON><PERSON>\App\Hook\Constrained;
use <PERSON><PERSON><PERSON>\App\Hook\Restriction;
use Captain<PERSON><PERSON>\App\Git\Rev\Util as RevUtil;
use CaptainHook\App\Hooks;
use <PERSON><PERSON><PERSON><PERSON>\Git\Repository;

/**
 * Class IntegrateBeforePush
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 5.19.1
 */
class IntegrateBeforePush implements Action, Constrained
{
    /**
     * Returns a list of applicable hooks
     *
     * @return \<PERSON><PERSON>ook\App\Hook\Restriction
     */
    public static function getRestriction(): Restriction
    {
        return Restriction::fromArray([Hooks::PRE_PUSH]);
    }

    /**
     * Executes the action
     *
     * @param  \CaptainHook\App\Config           $config
     * @param  \CaptainHook\App\Console\IO       $io
     * @param  \SebastianFeldmann\Git\Repository $repository
     * @param  \CaptainHook\App\Config\Action    $action
     * @return void
     * @throws \Exception
     */
    public function execute(Config $config, IO $io, Repository $repository, Config\Action $action): void
    {
        $trigger       = $action->getOptions()->get('trigger', '[merge]');
        $branchToWatch = $action->getOptions()->get('branch', 'origin/main');
        $branchInfo    = RevUtil::extractBranchInfo($branchToWatch);

        $repository->getRemoteOperator()->fetchBranch($branchInfo['remote'], $branchInfo['branch']);

        foreach ($repository->getLogOperator()->getCommitsBetween('HEAD', $branchToWatch) as $commit) {
            $message = $commit->getSubject() . PHP_EOL . $commit->getBody();
            if (str_contains($message, $trigger)) {
                throw new ActionFailed('integrate ' . $branchInfo['branch'] . ' before you push!');
            }
        }
    }
}
