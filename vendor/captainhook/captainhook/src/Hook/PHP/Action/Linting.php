<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace CaptainHook\App\Hook\PHP\Action;

use <PERSON><PERSON><PERSON>\App\Config;
use <PERSON><PERSON><PERSON>\App\Console\IO;
use <PERSON><PERSON><PERSON>\App\Console\IOUtil;
use <PERSON><PERSON>ook\App\Exception\ActionFailed;
use <PERSON><PERSON><PERSON>\App\Hook\Action;
use <PERSON><PERSON><PERSON><PERSON>\Cli\Processor\ProcOpen as Processor;
use <PERSON><PERSON><PERSON><PERSON>\Git\Repository;

/**
 * Class Linter
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 1.0.5
 */
class Linting implements Action
{
    /**
     * Path to php executable, default 'php'
     *
     * @var string
     */
    private $php;

    /**
     * Executes the action
     *
     * @param  \CaptainHook\App\Config           $config
     * @param  \CaptainHook\App\Console\IO       $io
     * @param  \Sebastian<PERSON><PERSON>mann\Git\Repository $repository
     * @param  \CaptainHook\App\Config\Action    $action
     * @return void
     * @throws \Exception
     */
    public function execute(Config $config, IO $io, Repository $repository, Config\Action $action): void
    {
        // we have to provide a custom filter because we do not want to check any deleted files
        $changedPHPFiles  = $repository->getIndexOperator()->getStagedFilesOfType('php', ['A', 'C', 'M']);
        $this->php        = !empty($config->getPhpPath()) ? $config->getPhpPath() : 'php';
        $failedFilesCount = 0;

        foreach ($changedPHPFiles as $file) {
            $prefix = IOUtil::PREFIX_OK;
            if ($this->hasSyntaxErrors($file)) {
                $failedFilesCount++;
                $io->write('  ' . IOUtil::PREFIX_FAIL . ' ' . $file, true, IO::NORMAL);
            }
            $io->write('  ' . $prefix . ' ' . $file, true, IO::VERBOSE);
        }

        if ($failedFilesCount > 0) {
            $s = $failedFilesCount > 1 ? 's' : '';
            throw new ActionFailed(
                'Linting failed: PHP syntax errors in ' . $failedFilesCount . ' file' . $s
            );
        }
    }

    /**
     * Lint a php file
     *
     * @param  string $file
     * @return bool
     */
    protected function hasSyntaxErrors(string $file): bool
    {
        $process = new Processor();
        $result  = $process->run($this->php . ' -l ' . escapeshellarg($file));

        return !$result->isSuccessful();
    }
}
