<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace <PERSON><PERSON>ook\App\Hook\Message\Rule;

use <PERSON><PERSON><PERSON><PERSON>\Git\CommitMessage;
use <PERSON><PERSON><PERSON>\App\Hook\Message\Rule;

/**
 * Class Base
 *
 * @package Captain<PERSON>ook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 0.9.0
 */
abstract class Base implements Rule
{
    /**
     * Rule hint.
     *
     * @var string
     */
    protected $hint;

    /**
     * @return string
     */
    public function getHint(): string
    {
        return $this->hint;
    }

    /**
     * @param  \Sebastian<PERSON>mann\Git\CommitMessage $msg
     * @return bool
     */
    abstract public function pass(CommitMessage $msg): bool;
}
