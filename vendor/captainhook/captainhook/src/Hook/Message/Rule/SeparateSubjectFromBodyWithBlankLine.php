<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace CaptainHook\App\Hook\Message\Rule;

use <PERSON><PERSON><PERSON><PERSON>\Git\CommitMessage;

/**
 * Class SeparateSubjectFromBodyWithBlankLine
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 0.9.0
 */
class SeparateSubjectFromBodyWithBlankLine extends Base
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->hint = 'Subject and body have to be separated by a blank line';
    }

    /**
     * Check if subject and body are separated by a blank line
     *
     * @param  \SebastianFeldmann\Git\CommitMessage $msg
     * @return bool
     */
    public function pass(CommitMessage $msg): bool
    {
        return $msg->getContentLineCount() < 2 || empty($msg->getContentLine(1));
    }
}
