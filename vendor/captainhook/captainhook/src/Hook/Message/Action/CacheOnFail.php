<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace <PERSON><PERSON>ook\App\Hook\Message\Action;

use <PERSON><PERSON><PERSON>\App\Config;
use Captain<PERSON><PERSON>\App\Console\IO;
use <PERSON><PERSON><PERSON>\App\Exception\ActionFailed;
use <PERSON><PERSON><PERSON>\App\Hook\Action;
use <PERSON><PERSON><PERSON>\App\Hook\EventSubscriber;
use <PERSON><PERSON><PERSON>\App\Hook\Message\EventHandler\WriteCacheFile;
use <PERSON><PERSON><PERSON><PERSON>\Git\Repository;

/**
 * Class FailedStore
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 5.11.0
 */
class CacheOnFail implements Action, EventSubscriber
{
    /**
     * Execute the configured action
     *
     * @param  \CaptainHook\App\Config           $config
     * @param  \CaptainHook\App\Console\IO       $io
     * @param  \Sebastian<PERSON><PERSON>mann\Git\Repository $repository
     * @param  \CaptainHook\App\Config\Action    $action
     * @return void
     * @throws \Exception
     */
    public function execute(Config $config, IO $io, Repository $repository, Config\Action $action): void
    {
        // this action is just registering some event handler, so nothing to see here
    }

    /**
     * Returns a list of event handlers
     *
     * @param  \CaptainHook\App\Config\Action   $action
     * @return array<string, array<int, \CaptainHook\App\Event\Handler>>
     * @throws \Exception
     */
    public static function getEventHandlers(Config\Action $action): array
    {
        // make sure the cache file is configured
        if (empty($action->getOptions()->get('file', ''))) {
            throw new ActionFailed('CacheOnFail requires \'file\' option');
        }
        return [
            'onHookFailure' => [new WriteCacheFile($action->getOptions()->get('file', ''))]
        ];
    }
}
