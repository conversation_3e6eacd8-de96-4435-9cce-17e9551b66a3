<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace CaptainHook\App\Hook\Message\Action;

use Captain<PERSON><PERSON>\App\Config;
use Captain<PERSON>ook\App\Console\IO;
use Captain<PERSON><PERSON>\App\Hook\Message\RuleBook;
use <PERSON><PERSON><PERSON><PERSON>\Git\Repository;

/**
 * Class Beams
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 0.9.0
 */
class Beams extends Book
{
    /**
     * Execute the configured action
     *
     * @param  \CaptainHook\App\Config           $config
     * @param  \CaptainHook\App\Console\IO       $io
     * @param  \SebastianFeldmann\Git\Repository $repository
     * @param  \CaptainHook\App\Config\Action    $action
     * @return void
     * @throws \Exception
     */
    public function execute(Config $config, IO $io, Repository $repository, Config\Action $action): void
    {
        $options = $action->getOptions();
        $book    = new RuleBook();
        $book->setRules(RuleBook\RuleSet::beams(
            (int)  $options->get('subjectLength', 50),
            (int)  $options->get('bodyLineLength', 72),
            (bool) $options->get('checkImperativeBeginningOnly', false)
        ));

        $this->validate($book, $repository, $io);
    }
}
