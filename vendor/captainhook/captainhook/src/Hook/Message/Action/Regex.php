<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace CaptainHook\App\Hook\Message\Action;

use <PERSON><PERSON><PERSON>\App\Config;
use <PERSON><PERSON><PERSON>\App\Console\IO;
use Captain<PERSON><PERSON>\App\Exception\ActionFailed;
use Captain<PERSON><PERSON>\App\Hook\Action;
use Captain<PERSON><PERSON>\App\Hook\Constrained;
use <PERSON><PERSON><PERSON>\App\Hook\Restriction;
use <PERSON><PERSON><PERSON>\App\Hooks;
use <PERSON><PERSON><PERSON><PERSON>\Git\Repository;

/**
 * Class Regex
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 1.0.0
 */
class Regex implements Action, Constrained
{
    /**
     * Return hook restriction
     *
     * @return \CaptainHook\App\Hook\Restriction
     */
    public static function getRestriction(): Restriction
    {
        return Restriction::fromArray([Hooks::COMMIT_MSG]);
    }

    /**
     * Execute the configured action
     *
     * @param  \CaptainHook\App\Config           $config
     * @param  \CaptainHook\App\Console\IO       $io
     * @param  \SebastianFeldmann\Git\Repository $repository
     * @param  \CaptainHook\App\Config\Action    $action
     * @return void
     * @throws \Exception
     */
    public function execute(Config $config, IO $io, Repository $repository, Config\Action $action): void
    {
        $regex      = $this->getRegex($action->getOptions());
        $errorMsg   = $this->getErrorMessage($action->getOptions());
        $successMsg = $this->getSuccessMessage($action->getOptions());
        $matches    = [];

        if ($repository->isMerging()) {
            return;
        }

        if (!preg_match($regex, $repository->getCommitMsg()->getContent(), $matches)) {
            throw new ActionFailed(sprintf($errorMsg, $regex));
        }

        $io->write(['', '', sprintf($successMsg, $matches[0]), ''], true, IO::VERBOSE);
    }

    /**
     * Extract regex from options array
     *
     * @param  \CaptainHook\App\Config\Options $options
     * @return string
     * @throws \CaptainHook\App\Exception\ActionFailed
     */
    protected function getRegex(Config\Options $options): string
    {
        $regex = $options->get('regex', '');
        if (empty($regex)) {
            throw new ActionFailed('No regex option');
        }
        return $regex;
    }

    /**
     * Determine the error message to use
     *
     * @param  \CaptainHook\App\Config\Options $options
     * @return string
     */
    protected function getErrorMessage(Config\Options $options): string
    {
        $msg = $options->get('error', '');
        return !empty($msg) ? $msg : 'Commit message did not match regex: %s';
    }

    /**
     * Determine the error message to use
     *
     * @param  \CaptainHook\App\Config\Options $options
     * @return string
     */
    protected function getSuccessMessage(Config\Options $options): string
    {
        $msg = $options->get('success', '');
        return !empty($msg) ? $msg : 'Found matching pattern: %s';
    }
}
