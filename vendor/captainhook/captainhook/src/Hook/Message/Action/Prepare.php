<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace CaptainHook\App\Hook\Message\Action;

use <PERSON><PERSON><PERSON>\App\Config;
use Captain<PERSON><PERSON>\App\Console\IO;
use Captain<PERSON><PERSON>\App\Hook\Action;
use <PERSON><PERSON><PERSON><PERSON>\Git\CommitMessage;
use <PERSON><PERSON><PERSON><PERSON>\Git\Repository;

/**
 * Class Prepare
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 3.1.0
 */
class Prepare implements Action
{
    /**
     * Executes the action
     *
     * @param  \CaptainHook\App\Config           $config
     * @param  \CaptainHook\App\Console\IO       $io
     * @param  \SebastianFeldmann\Git\Repository $repository
     * @param  \CaptainHook\App\Config\Action    $action
     * @return void
     * @throws \Exception
     */
    public function execute(Config $config, IO $io, Repository $repository, Config\Action $action): void
    {
        $options = $action->getOptions();
        $oldMsg  = $repository->getCommitMsg();

        if (!$repository->isMerging()) {
            $repository->setCommitMsg(new CommitMessage($options->get('message', ''), $oldMsg->getCommentCharacter()));
        }
    }
}
