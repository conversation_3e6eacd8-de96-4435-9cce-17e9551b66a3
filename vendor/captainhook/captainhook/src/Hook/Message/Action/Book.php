<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace CaptainHook\App\Hook\Message\Action;

use Captain<PERSON><PERSON>\App\Config;
use <PERSON><PERSON><PERSON>\App\Console\IO;
use <PERSON><PERSON><PERSON>\App\Console\IOUtil;
use Captain<PERSON>ook\App\Exception\ActionFailed;
use <PERSON><PERSON><PERSON>\App\Hook\Action;
use <PERSON><PERSON>ook\App\Hook\Constrained;
use Captain<PERSON><PERSON>\App\Hook\Message\RuleBook;
use Captain<PERSON>ook\App\Hook\Restriction;
use Captain<PERSON><PERSON>\App\Hooks;
use <PERSON><PERSON><PERSON><PERSON>\Cli\Output\Util as OutputUtil;
use <PERSON><PERSON><PERSON><PERSON>\Git\Repository;

/**
 * Class Book
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 0.9.0
 */
abstract class Book implements Action, Constrained
{
    /**
     * Returns a list of applicable hooks
     *
     * @return \CaptainHook\App\Hook\Restriction
     */
    public static function getRestriction(): Restriction
    {
        return Restriction::fromArray([Hooks::COMMIT_MSG]);
    }

    /**
     * Execute the configured action
     *
     * @param  \CaptainHook\App\Config           $config
     * @param  \CaptainHook\App\Console\IO       $io
     * @param  \SebastianFeldmann\Git\Repository $repository
     * @param  \CaptainHook\App\Config\Action    $action
     * @return void
     * @throws \Exception
     */
    abstract public function execute(Config $config, IO $io, Repository $repository, Config\Action $action): void;

    /**
     * Validate the message
     *
     * @param  \CaptainHook\App\Hook\Message\RuleBook $ruleBook
     * @param  \SebastianFeldmann\Git\Repository      $repository
     * @param  \CaptainHook\App\Console\IO            $io
     * @return void
     * @throws \CaptainHook\App\Exception\ActionFailed
     */
    protected function validate(RuleBook $ruleBook, Repository $repository, IO $io): void
    {
        // if this is a merge commit skip enforcing message rules
        if ($repository->isMerging()) {
            return;
        }

        $problems = $ruleBook->validate($repository->getCommitMsg());

        if (count($problems)) {
            $this->errorOutput($problems, $io, $repository);
            throw new ActionFailed('commit message validation failed');
        }
    }

    /**
     * Write the error message
     *
     * @param array<string>                     $problems
     * @param \CaptainHook\App\Console\IO       $io
     * @param \SebastianFeldmann\Git\Repository $repository
     * @return void
     */
    private function errorOutput(array $problems, IO $io, Repository $repository): void
    {
        $s = count($problems) > 1 ? 's' : '';
        $io->write('found ' . count($problems) . ' problem' . $s . ' in your commit message');
        foreach ($problems as $problem) {
            $io->write($this->formatProblem($problem));
        }
        $io->write('<comment>--------------------------------------------[ your original message ]----</comment>');
        $io->write(OutputUtil::trimEmptyLines($repository->getCommitMsg()->getLines()));
        $io->write('<comment>-------------------------------------------------------------------------</comment>');
    }

    /**
     * Indent multi line problems so the lines after the first one are indented for better readability
     *
     * @param  string $problem
     * @return array<string>
     */
    private function formatProblem(string $problem): array
    {
        $lines = explode(PHP_EOL, $problem);
        foreach ($lines as $index => $line) {
            $lines[$index] = '  ' . $line;
        }
        return $lines;
    }
}
