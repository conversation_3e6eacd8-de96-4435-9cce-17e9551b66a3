<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace CaptainHook\App\Hook\File\Action;

use <PERSON><PERSON><PERSON><PERSON>\Git\Repository;

/**
 * Class IsNotEmpty
 *
 * @package Captain<PERSON>ook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 5.4.1
 */
class IsNotEmpty extends Emptiness
{
    /**
     * Actual action name for better error messages
     *
     * @var string
     */
    protected string $actionName = 'IsNotEmpty';

    /**
     * Checks if the file is valid or not
     *
     * @param  \SebastianFeldmann\Git\Repository $repository
     * @param  string                            $file
     * @return bool
     */
    protected function isValid(Repository $repository, string $file): bool
    {
        return !$this->isEmpty($file);
    }
}
