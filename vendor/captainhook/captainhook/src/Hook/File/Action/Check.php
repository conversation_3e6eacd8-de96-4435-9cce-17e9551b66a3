<?php

/**
 * This file is part of Captain<PERSON><PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace CaptainHook\App\Hook\File\Action;

use <PERSON><PERSON><PERSON>\App\Config;
use <PERSON><PERSON><PERSON>\App\Console\IO;
use <PERSON><PERSON><PERSON>\App\Console\IOUtil;
use <PERSON><PERSON>ook\App\Exception\ActionFailed;
use <PERSON><PERSON><PERSON>\App\Hook\Action;
use <PERSON><PERSON>ook\App\Hook\Constrained;
use <PERSON><PERSON><PERSON>\App\Hook\Restriction;
use <PERSON><PERSON><PERSON><PERSON>\Git\Repository;

/**
 * Class Check
 *
 * @package CaptainHook
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 5.4.1
 */
abstract class Check implements Action, Constrained
{
    /**
     * Actual action name
     *
     * @var string
     */
    protected string $actionName;

    /**
     * Make sure this action is only used pro pre-commit hooks
     *
     * @return \CaptainHook\App\Hook\Restriction
     */
    public static function getRestriction(): Restriction
    {
        return new Restriction('pre-commit');
    }

    /**
     * Executes the action
     *
     * @param  \CaptainHook\App\Config           $config
     * @param  \CaptainHook\App\Console\IO       $io
     * @param  \SebastianFeldmann\Git\Repository $repository
     * @param  \CaptainHook\App\Config\Action    $action
     * @return void
     * @throws \Exception
     */
    public function execute(Config $config, IO $io, Repository $repository, Config\Action $action): void
    {
        $this->setUp($action->getOptions());

        $filesToCheck = $this->getFilesToCheck($repository);
        $filesFailed  = 0;

        if (!count($filesToCheck)) {
            $io->write('  no files had to be checked', true, IO::VERBOSE);
            return;
        }

        foreach ($filesToCheck as $file) {
            if (!$this->isValid($repository, $file)) {
                $io->write('  ' . IOUtil::PREFIX_FAIL . ' ' . $file . $this->errorDetails($file));
                $filesFailed++;
                continue;
            }
            $io->write('  ' . IOUtil::PREFIX_OK . ' ' . $file, true, IO::VERBOSE);
        }

        if ($filesFailed > 0) {
            throw new ActionFailed(
                $this->errorMessage($filesFailed)
            );
        }
    }

    /**
     * Setup the action, reading and validating all config settings
     *
     * @param \CaptainHook\App\Config\Options $options
     */
    protected function setUp(Config\Options $options): void
    {
        // can be used in child classes to extract and validate config settings
    }

    /**
     * Some output appendix for every file
     *
     * @param  string $file
     * @return string
     */
    protected function errorDetails(string $file): string
    {
        // can be used to enhance the output
        return '';
    }

    /**
     * Define the exception error message
     *
     * @param  int $filesFailed
     * @return string
     */
    protected function errorMessage(int $filesFailed): string
    {
        $s = $filesFailed > 1 ? 's' : '';
        return '<error>Error: ' . $filesFailed . ' file' . $s . ' failed</error>';
    }

    /**
     * Determine if the file is valid
     *
     * @param  \SebastianFeldmann\Git\Repository $repository
     * @param  string                            $file
     * @return bool
     */
    abstract protected function isValid(Repository $repository, string $file): bool;

    /**
     * Return the list of files that should be checked
     *
     * @param  \SebastianFeldmann\Git\Repository $repository
     * @return array<string>
     */
    protected function getFilesToCheck(Repository $repository): array
    {
        return $repository->getIndexOperator()->getStagedFiles();
    }
}
