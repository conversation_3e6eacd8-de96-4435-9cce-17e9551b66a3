<?php

/**
 * This file is part of CaptainH<PERSON>
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace CaptainHook\App\Hook\Template\Local;

/**
 * WSL class
 *
 * Generates the sourcecode for the php hook scripts in .git/hooks/*.
 *
 * @package Captain<PERSON>ook
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/captainhook-git/captainhook
 * @since   Class available since Release 5.23.0
 */
class WSL extends Shell
{
    protected function getExecutable(): string
    {
        return 'wsl.exe ' . parent::getExecutable();
    }
}
