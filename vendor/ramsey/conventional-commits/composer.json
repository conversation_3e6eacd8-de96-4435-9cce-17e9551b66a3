{"name": "ramsey/conventional-commits", "description": "A PHP library for creating and validating commit messages according to the Conventional Commits specification. Includes a CaptainHook action!", "license": "MIT", "type": "library", "keywords": ["captainhook", "commit", "commit-msg", "conventional", "conventional-commits", "git", "hook", "plugin"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "require": {"php": "^8.1", "ext-json": "*", "composer-runtime-api": "^2.0", "composer/composer": "^2.4", "jawira/case-converter": "^3.5", "opis/json-schema": "^2.3", "symfony/console": "^6.0 || ^7.0", "symfony/filesystem": "^6.0 || ^7.0"}, "require-dev": {"captainhook/captainhook": "^5.15", "captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.30", "hamcrest/hamcrest-php": "^2.0", "mockery/mockery": "^1.5", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.10", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^10.1", "ramsey/coding-standard": "^2.3", "ramsey/composer-repl": "^1.4", "roave/security-advisories": "dev-latest", "sebastianfeldmann/cli": "^3.4", "sebastianfeldmann/git": "^3.8", "spatie/phpunit-snapshot-assertions": "^5.1", "symfony/process": "^6.0 || ^7.0"}, "suggest": {"captainhook/captainhook": "Manage your project's Git hooks with <PERSON><PERSON><PERSON>, and use ramsey/conventional-commits in your commit-msg and prepare-commit-msg hooks."}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Ramsey\\CaptainHook\\": "src/<PERSON><PERSON><PERSON>/", "Ramsey\\ConventionalCommits\\": "src/ConventionalCommits/"}}, "autoload-dev": {"psr-4": {"Ramsey\\Test\\": "tests/"}}, "bin": ["bin/conventional-commits"], "config": {"allow-plugins": {"captainhook/plugin-composer": true, "dealerdirect/phpcodesniffer-composer-installer": true, "ergebnis/composer-normalize": true, "phpstan/extension-installer": true, "ramsey/composer-repl": true}, "sort-packages": true}, "extra": {"branch-alias": {"dev-main": "1.x-dev"}, "captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "scripts": {"dev:analyze": ["@dev:analyze:phpstan"], "dev:analyze:phpstan": "phpstan analyse --ansi --memory-limit 256M", "dev:build:clean": "git clean -fX build/", "dev:lint": ["@dev:lint:syntax", "@dev:lint:style"], "dev:lint:fix": "phpcbf", "dev:lint:style": "phpcs --colors", "dev:lint:syntax": "parallel-lint --colors src/ tests/", "dev:test": ["@dev:lint", "@dev:analyze", "@dev:test:unit"], "dev:test:coverage:ci": "phpunit --colors=always --coverage-text --coverage-clover build/coverage/clover.xml --coverage-cobertura build/coverage/cobertura.xml --coverage-crap4j build/coverage/crap4j.xml --coverage-xml build/coverage/coverage-xml --log-junit build/junit.xml", "dev:test:coverage:html": "phpunit --colors=always --coverage-html build/coverage/coverage-html/", "dev:test:functional": "bats tests/functional", "dev:test:unit": "phpunit --colors=always", "test": "@dev:test", "test-functional": "@dev:test:functional"}, "scripts-descriptions": {"dev:analyze": "Runs all static analysis checks.", "dev:analyze:phpstan": "Runs the PHPStan static analyzer.", "dev:build:clean": "Cleans the build/ directory.", "dev:lint": "Runs all linting checks.", "dev:lint:fix": "Auto-fixes coding standards issues, if possible.", "dev:lint:style": "Checks for coding standards issues.", "dev:lint:syntax": "Checks for syntax errors.", "dev:test": "Runs linting, static analysis, and unit tests.", "dev:test:coverage:ci": "Runs unit tests and generates CI coverage reports.", "dev:test:coverage:html": "Runs unit tests and generates HTML coverage report.", "dev:test:functional": "Runs functional tests.", "dev:test:unit": "Runs unit tests.", "test": "Runs linting, static analysis, and unit tests.", "test-functional": "Runs functional tests."}}