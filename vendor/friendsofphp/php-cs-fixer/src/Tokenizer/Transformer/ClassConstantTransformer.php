<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace Php<PERSON><PERSON>ixer\Tokenizer\Transformer;

use <PERSON>pCs<PERSON><PERSON>er\Tokenizer\AbstractTransformer;
use PhpCsF<PERSON><PERSON>\Tokenizer\CT;
use <PERSON>p<PERSON>F<PERSON>er\Tokenizer\Token;
use PhpCsF<PERSON>er\Tokenizer\Tokens;

/**
 * Transform `class` class' constant from T_CLASS into CT::T_CLASS_CONSTANT.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
final class ClassConstantTransformer extends AbstractTransformer
{
    public function getRequiredPhpVersionId(): int
    {
        return 5_05_00;
    }

    public function process(Tokens $tokens, Token $token, int $index): void
    {
        if (!$token->equalsAny([
            [T_CLASS, 'class'],
            [T_STRING, 'class'],
        ], false)) {
            return;
        }

        $prevIndex = $tokens->getPrevMeaningfulToken($index);
        $prevToken = $tokens[$prevIndex];

        if ($prevToken->isGivenKind(T_DOUBLE_COLON)) {
            $tokens[$index] = new Token([CT::T_CLASS_CONSTANT, $token->getContent()]);
        }
    }

    public function getCustomTokens(): array
    {
        return [CT::T_CLASS_CONSTANT];
    }
}
