<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace Php<PERSON><PERSON>ixer\Tokenizer\Transformer;

use PhpCs<PERSON><PERSON>er\Tokenizer\AbstractTransformer;
use PhpCsFixer\Tokenizer\CT;
use <PERSON>p<PERSON>Fixer\Tokenizer\Token;
use PhpCsFixer\Tokenizer\Tokens;

/**
 * @internal
 */
final class FirstClassCallableTransformer extends AbstractTransformer
{
    public function getRequiredPhpVersionId(): int
    {
        return 8_01_00;
    }

    public function process(Tokens $tokens, Token $token, int $index): void
    {
        if (
            $token->isGivenKind(T_ELLIPSIS)
            && $tokens[$tokens->getPrevMeaningfulToken($index)]->equals('(')
            && $tokens[$tokens->getNextMeaningfulToken($index)]->equals(')')
        ) {
            $tokens[$index] = new Token([CT::T_FIRST_CLASS_CALLABLE, '...']);
        }
    }

    public function getCustomTokens(): array
    {
        return [
            CT::T_FIRST_CLASS_CALLABLE,
        ];
    }
}
