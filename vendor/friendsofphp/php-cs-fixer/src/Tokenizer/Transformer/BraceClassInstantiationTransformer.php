<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace Php<PERSON><PERSON>ixer\Tokenizer\Transformer;

use <PERSON>pCs<PERSON><PERSON><PERSON>\Tokenizer\AbstractTransformer;
use PhpCsF<PERSON>er\Tokenizer\CT;
use <PERSON>p<PERSON>F<PERSON>er\Tokenizer\Token;
use <PERSON>pCsF<PERSON>er\Tokenizer\Tokens;

/**
 * Transform braced class instantiation braces in `(new Foo())` into CT::T_BRACE_CLASS_INSTANTIATION_OPEN
 * and CT::T_BRACE_CLASS_INSTANTIATION_CLOSE.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
final class BraceClassInstantiationTransformer extends AbstractTransformer
{
    public function getPriority(): int
    {
        // must run after CurlyBraceTransformer and SquareBraceTransformer
        return -2;
    }

    public function getRequiredPhpVersionId(): int
    {
        return 5_00_00;
    }

    public function process(Tokens $tokens, Token $token, int $index): void
    {
        if (!$tokens[$index]->equals('(') || !$tokens[$tokens->getNextMeaningfulToken($index)]->isGivenKind(T_NEW)) {
            return;
        }

        if ($tokens[$tokens->getPrevMeaningfulToken($index)]->equalsAny([
            ')',
            ']',
            [CT::T_ARRAY_INDEX_CURLY_BRACE_CLOSE],
            [CT::T_ARRAY_SQUARE_BRACE_CLOSE],
            [CT::T_BRACE_CLASS_INSTANTIATION_CLOSE],
            [T_ARRAY],
            [T_CLASS],
            [T_ELSEIF],
            [T_FOR],
            [T_FOREACH],
            [T_IF],
            [T_STATIC],
            [T_STRING],
            [T_SWITCH],
            [T_VARIABLE],
            [T_WHILE],
        ])) {
            return;
        }

        $closeIndex = $tokens->findBlockEnd(Tokens::BLOCK_TYPE_PARENTHESIS_BRACE, $index);

        $tokens[$index] = new Token([CT::T_BRACE_CLASS_INSTANTIATION_OPEN, '(']);
        $tokens[$closeIndex] = new Token([CT::T_BRACE_CLASS_INSTANTIATION_CLOSE, ')']);
    }

    public function getCustomTokens(): array
    {
        return [CT::T_BRACE_CLASS_INSTANTIATION_OPEN, CT::T_BRACE_CLASS_INSTANTIATION_CLOSE];
    }
}
