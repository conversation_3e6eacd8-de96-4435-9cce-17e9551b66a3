<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) F<PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace Php<PERSON><PERSON><PERSON>er\Tokenizer\Transformer;

use <PERSON>pCs<PERSON><PERSON><PERSON>\Tokenizer\AbstractTypeTransformer;
use PhpCsF<PERSON>er\Tokenizer\CT;
use <PERSON>p<PERSON>F<PERSON>er\Tokenizer\Token;
use PhpCsFixer\Tokenizer\Tokens;

/**
 * Transform `|` operator into CT::T_TYPE_ALTERNATION in `function foo(Type1 | Type2 $x) {`
 * or `} catch (ExceptionType1 | ExceptionType2 $e) {`.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
final class TypeAlternationTransformer extends AbstractTypeTransformer
{
    public function getPriority(): int
    {
        // needs to run after ArrayTypehintTransformer, TypeColonTransformer and AttributeTransformer
        return -15;
    }

    public function getRequiredPhpVersionId(): int
    {
        return 7_01_00;
    }

    public function process(Tokens $tokens, Token $token, int $index): void
    {
        $this->doProcess($tokens, $index, '|');
    }

    public function getCustomTokens(): array
    {
        return [CT::T_TYPE_ALTERNATION];
    }

    protected function replaceToken(Tokens $tokens, int $index): void
    {
        $tokens[$index] = new Token([CT::T_TYPE_ALTERNATION, '|']);
    }
}
