<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace Php<PERSON><PERSON>ixer\Tokenizer\Transformer;

use <PERSON>p<PERSON><PERSON><PERSON><PERSON>\Tokenizer\AbstractTransformer;
use PhpCsF<PERSON><PERSON>\Tokenizer\CT;
use <PERSON>p<PERSON>F<PERSON>er\Tokenizer\Token;
use <PERSON>p<PERSON>F<PERSON>er\Tokenizer\Tokens;

/**
 * Transform DNF parentheses into CT::T_DISJUNCTIVE_NORMAL_FORM_TYPE_PARENTHESIS_OPEN and CT::T_DISJUNCTIVE_NORMAL_FORM_TYPE_PARENTHESIS_CLOSE.
 *
 * @see https://wiki.php.net/rfc/dnf_types
 *
 * @internal
 */
final class DisjunctiveNormalFormTypeParenthesisTransformer extends AbstractTransformer
{
    public function getPriority(): int
    {
        // needs to run after TypeAlternationTransformer
        return -16;
    }

    public function getRequiredPhpVersionId(): int
    {
        return 8_02_00;
    }

    public function process(Tokens $tokens, Token $token, int $index): void
    {
        if ($token->equals('(') && $tokens[$tokens->getPrevMeaningfulToken($index)]->isGivenKind(CT::T_TYPE_ALTERNATION)) {
            $openIndex = $index;
            $closeIndex = $tokens->findBlockEnd(Tokens::BLOCK_TYPE_PARENTHESIS_BRACE, $index);
        } elseif ($token->equals(')') && $tokens[$tokens->getNextMeaningfulToken($index)]->isGivenKind(CT::T_TYPE_ALTERNATION)) {
            $openIndex = $tokens->findBlockStart(Tokens::BLOCK_TYPE_PARENTHESIS_BRACE, $index);
            $closeIndex = $index;
        } else {
            return;
        }

        $tokens[$openIndex] = new Token([CT::T_DISJUNCTIVE_NORMAL_FORM_TYPE_PARENTHESIS_OPEN, '(']);
        $tokens[$closeIndex] = new Token([CT::T_DISJUNCTIVE_NORMAL_FORM_TYPE_PARENTHESIS_CLOSE, ')']);
    }

    public function getCustomTokens(): array
    {
        return [
            CT::T_DISJUNCTIVE_NORMAL_FORM_TYPE_PARENTHESIS_OPEN,
            CT::T_DISJUNCTIVE_NORMAL_FORM_TYPE_PARENTHESIS_CLOSE,
        ];
    }
}
