<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Fixer\Whitespace;

use PhpCs<PERSON><PERSON>er\AbstractProxyFixer;
use PhpCsFixer\Fixer\DeprecatedFixerInterface;
use PhpCsFixer\FixerDefinition\FixerDefinition;
use PhpCsFixer\FixerDefinition\FixerDefinitionInterface;

/**
 * <AUTHOR> <PERSON>ern<PERSON> <<EMAIL>>
 *
 * @deprecated
 */
final class CompactNullableTypehintFixer extends AbstractProxyFixer implements DeprecatedFixerInterface
{
    private CompactNullableTypeDeclarationFixer $compactNullableTypeDeclarationFixer;

    public function __construct()
    {
        $this->compactNullableTypeDeclarationFixer = new CompactNullableTypeDeclarationFixer();

        parent::__construct();
    }

    public function getDefinition(): FixerDefinitionInterface
    {
        $fixerDefinition = $this->compactNullableTypeDeclarationFixer->getDefinition();

        return new FixerDefinition(
            'Remove extra spaces in a nullable typehint.',
            $fixerDefinition->getCodeSamples(),
            $fixerDefinition->getDescription(),
            $fixerDefinition->getRiskyDescription(),
        );
    }

    public function getSuccessorsNames(): array
    {
        return [
            $this->compactNullableTypeDeclarationFixer->getName(),
        ];
    }

    protected function createProxyFixers(): array
    {
        return [
            $this->compactNullableTypeDeclarationFixer,
        ];
    }
}
