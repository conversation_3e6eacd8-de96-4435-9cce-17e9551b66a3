<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Runner\Parallel;

/**
 * Common exception for all the errors related to parallelisation.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
final class ParallelisationException extends \RuntimeException
{
    public static function forUnknownIdentifier(ProcessIdentifier $identifier): self
    {
        return new self('Unknown process identifier: '.$identifier->toString());
    }
}
