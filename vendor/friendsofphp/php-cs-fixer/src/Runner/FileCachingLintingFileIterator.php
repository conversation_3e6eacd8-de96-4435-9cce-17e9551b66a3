<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) Fabi<PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Runner;

use PhpCsF<PERSON>er\Linter\LinterInterface;
use <PERSON>pCsF<PERSON>er\Linter\LintingResultInterface;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 *
 * @extends \CachingIterator<mixed, \SplFileInfo, \Iterator<mixed, \SplFileInfo>>
 */
final class FileCachingLintingFileIterator extends \CachingIterator implements LintingResultAwareFileIteratorInterface
{
    private LinterInterface $linter;
    private ?LintingResultInterface $currentResult = null;
    private ?LintingResultInterface $nextResult = null;

    /**
     * @param \Iterator<mixed, \SplFileInfo> $iterator
     */
    public function __construct(\Iterator $iterator, LinterInterface $linter)
    {
        parent::__construct($iterator);

        $this->linter = $linter;
    }

    public function currentLintingResult(): ?LintingResultInterface
    {
        return $this->currentResult;
    }

    public function next(): void
    {
        parent::next();

        $this->currentResult = $this->nextResult;

        if ($this->hasNext()) {
            $this->nextResult = $this->handleItem($this->getInnerIterator()->current());
        }
    }

    public function rewind(): void
    {
        parent::rewind();

        if ($this->valid()) {
            $this->currentResult = $this->handleItem($this->current());
        }

        if ($this->hasNext()) {
            $this->nextResult = $this->handleItem($this->getInnerIterator()->current());
        }
    }

    private function handleItem(\SplFileInfo $file): LintingResultInterface
    {
        return $this->linter->lintFile($file->getRealPath());
    }
}
