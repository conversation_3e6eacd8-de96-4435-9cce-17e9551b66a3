<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Console\Output\Progress;

/**
 * @internal
 */
final class ProgressOutputType
{
    public const NONE = 'none';
    public const DOTS = 'dots';
    public const BAR = 'bar';

    /**
     * @return list<ProgressOutputType::*>
     */
    public static function all(): array
    {
        return [
            self::BAR,
            self::DOTS,
            self::NONE,
        ];
    }
}
