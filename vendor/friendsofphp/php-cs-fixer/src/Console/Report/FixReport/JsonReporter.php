<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Console\Report\FixReport;

use PhpCsFixer\Console\Application;
use Symfony\Component\Console\Formatter\OutputFormatter;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @readonly
 *
 * @internal
 */
final class JsonReporter implements ReporterInterface
{
    public function getFormat(): string
    {
        return 'json';
    }

    public function generate(ReportSummary $reportSummary): string
    {
        $jsonFiles = [];

        foreach ($reportSummary->getChanged() as $file => $fixResult) {
            $jsonFile = ['name' => $file];

            if ($reportSummary->shouldAddAppliedFixers()) {
                $jsonFile['appliedFixers'] = $fixResult['appliedFixers'];
            }

            if ('' !== $fixResult['diff']) {
                $jsonFile['diff'] = $fixResult['diff'];
            }

            $jsonFiles[] = $jsonFile;
        }

        $json = [
            'about' => Application::getAbout(),
            'files' => $jsonFiles,
            'time' => [
                'total' => round($reportSummary->getTime() / 1_000, 3),
            ],
            'memory' => round($reportSummary->getMemory() / 1_024 / 1_024, 3),
        ];

        $json = json_encode($json, JSON_THROW_ON_ERROR);

        return $reportSummary->isDecoratedOutput() ? OutputFormatter::escape($json) : $json;
    }
}
