{"name": "opis/uri", "description": "Build, parse and validate URIs and URI-templates", "keywords": ["uri", "uri template", "url", "parse url", "uri components", "validate uri", "punycode"], "homepage": "https://opis.io", "license": "Apache-2.0", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "require": {"php": "^7.4 || ^8.0", "opis/string": "^2.0"}, "require-dev": {"phpunit/phpunit": "^9"}, "autoload": {"psr-4": {"Opis\\Uri\\": "src/"}}, "autoload-dev": {"psr-4": {"Opis\\Uri\\Test\\": "tests/"}}, "config": {"preferred-install": "dist"}, "prefer-stable": true, "minimum-stability": "dev", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}}