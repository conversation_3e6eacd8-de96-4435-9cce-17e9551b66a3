Opis URI
==========
[![Tests](https://github.com/opis/uri/workflows/Tests/badge.svg)](https://github.com/opis/uri/actions)
[![Latest Stable Version](https://poser.pugx.org/opis/uri/version.png)](https://packagist.org/packages/opis/uri)
[![Latest Unstable Version](https://poser.pugx.org/opis/uri/v/unstable.png)](//packagist.org/packages/opis/uri)
[![License](https://poser.pugx.org/opis/uri/license.png)](https://packagist.org/packages/opis/uri)


**Opis URI** library allows you to build, parse and validate URIs and URI-templates.

## License

**Opis URI** is licensed under the [Apache License, Version 2.0][license]. 

## Requirements

* PHP ^7.4 || ^8.0

## Installation

**Opis URI** is available on [Packagist] and it can be installed from a 
command line interface by using [Composer]. 

```bash
composer require opis/uri
```

Or you could directly reference it into your `composer.json` file as a dependency

```json
{
    "require": {
        "opis/uri": "^1.0"
    }
}
```

[license]: https://www.apache.org/licenses/LICENSE-2.0 "Apache License"
[Packagist]: https://packagist.org/packages/opis/database "Packagist"
[Composer]: https://getcomposer.org "Composer"