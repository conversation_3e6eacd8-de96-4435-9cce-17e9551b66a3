{"name": "opis/json-schema", "description": "<PERSON><PERSON> Schema Validator for PHP", "keywords": ["json", "schema", "json-schema", "validation", "validator"], "homepage": "https://opis.io/json-schema", "license": "Apache-2.0", "authors": [{"name": "Sorin Sarca", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.4 || ^8.0", "ext-json": "*", "opis/string": "^2.0", "opis/uri": "^1.0"}, "require-dev": {"ext-bcmath": "*", "ext-intl": "*", "phpunit/phpunit": "^9.0"}, "autoload": {"psr-4": {"Opis\\JsonSchema\\": "src/"}}, "autoload-dev": {"psr-4": {"Opis\\JsonSchema\\Test\\": "tests/"}}, "scripts": {"tests": "./vendor/bin/phpunit --verbose --color"}, "extra": {"branch-alias": {"dev-master": "2.x-dev"}}}