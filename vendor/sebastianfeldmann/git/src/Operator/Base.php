<?php

/**
 * This file is part of <PERSON><PERSON><PERSON><PERSON>\Git.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace <PERSON><PERSON><PERSON><PERSON>\Git\Operator;

use <PERSON><PERSON><PERSON><PERSON>\Cli\Command\Runner;
use <PERSON><PERSON><PERSON><PERSON>\Git\Repository;

/**
 * Class Base
 *
 * @package <PERSON><PERSON><PERSON>mann\Git
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/sebastian<PERSON>mann/git
 * @since   Class available since Release 0.9.0
 */
abstract class Base
{
    /**
     * Runner to execute git system calls.
     *
     * @var \Sebastian<PERSON>eldmann\Cli\Command\Runner
     */
    protected Runner $runner;

    /**
     * Git repository to use.
     *
     * @var \SebastianFeldmann\Git\Repository
     */
    protected Repository $repo;

    /**
     * Base constructor.
     *
     * @param \Sebastian<PERSON><PERSON>mann\Cli\Command\Runner $runner
     * @param \Sebastian<PERSON><PERSON>mann\Git\Repository     $repo
     */
    public function __construct(Runner $runner, Repository $repo)
    {
        $this->runner = $runner;
        $this->repo   = $repo;
    }
}
