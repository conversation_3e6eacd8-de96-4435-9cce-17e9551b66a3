<?php

/**
 * This file is part of <PERSON><PERSON><PERSON><PERSON>\Git.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace <PERSON><PERSON><PERSON><PERSON>\Git\Command\Status;

use <PERSON><PERSON><PERSON><PERSON>\Git\Command\Base;

/**
 * Class GetWorkingTreeStatus
 *
 * @package <PERSON><PERSON><PERSON>mann\Git
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/sebastian<PERSON>mann/git
 * @since   Class available since Release 3.6.0
 */
class WorkingTreeStatus extends Base
{
    /**
     * Ignore submodules.
     *
     * @var string
     */
    private string $ignoreSubmodules = '';

    /**
     * Set ignore submodules.
     *
     * @param  bool $bool
     *
     * @return \SebastianF<PERSON>mann\Git\Command\Status\WorkingTreeStatus
     */
    public function ignoreSubmodules(bool $bool = true): WorkingTreeStatus
    {
        $this->ignoreSubmodules = $this->useOption('--ignore-submodules', $bool);
        return $this;
    }

    /**
     * Return the command to execute.
     *
     * @return string
     * @throws \RuntimeException
     */
    protected function getGitCommand(): string
    {
        return 'status --porcelain=v1 -z'
               . $this->ignoreSubmodules;
    }
}
