<?php

/**
 * This file is part of <PERSON><PERSON><PERSON><PERSON>\Git.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace <PERSON><PERSON><PERSON><PERSON>\Git\Command\Branch;

use <PERSON><PERSON><PERSON><PERSON>\Git\Command\Base;

/**
 * Class ListRemote
 *
 * @package <PERSON><PERSON><PERSON><PERSON>\Git
 * <AUTHOR>
 */
class ListRemote extends Base
{
    /**
     * Return the command to execute
     *
     * @return string
     */
    protected function getGitCommand(): string
    {
        return 'branch -r';
    }
}
