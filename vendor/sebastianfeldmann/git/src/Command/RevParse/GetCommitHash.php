<?php

/**
 * This file is part of <PERSON><PERSON><PERSON><PERSON>\Git.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace <PERSON><PERSON><PERSON><PERSON>\Git\Command\RevParse;

use <PERSON><PERSON><PERSON><PERSON>\Git\Command\Base;

/**
 * Class GetCommitHash
 *
 * @package <PERSON><PERSON><PERSON><PERSON>\Git
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/sebastian<PERSON>mann/git
 * @since   Class available since Release 0.9.0
 */
class GetCommitHash extends Base
{
    /**
     * Revision to look up.
     *
     * @var string
     */
    private string $rev = 'HEAD';

    /**
     * Set revision to look up.
     *
     * @param  string $revision
     * @return \Sebastian<PERSON><PERSON>mann\Git\Command\RevParse\GetCommitHash
     */
    public function revision(string $revision): GetCommitHash
    {
        $this->rev = $revision;
        return $this;
    }

    /**
     * Return the command to execute.
     *
     * @return string
     */
    protected function getGitCommand(): string
    {
        return 'rev-parse --verify ' . $this->rev;
    }
}
