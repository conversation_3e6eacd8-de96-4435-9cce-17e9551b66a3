<?php

/**
 * This file is part of <PERSON><PERSON><PERSON><PERSON>\Git.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace <PERSON><PERSON><PERSON><PERSON>\Git\Command\Config;

use <PERSON><PERSON><PERSON><PERSON>\Cli\Command\OutputFormatter;

/**
 * Class MapSettings
 *
 * @package <PERSON><PERSON><PERSON>mann\Git
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/sebastian<PERSON>mann/git
 * @since   Class available since Release 1.0.8
 */
class MapValues implements OutputFormatter
{
    /**
     * Format the output
     *
     * @param  array<string> $output
     * @return iterable<string, string>
     */
    public function format(array $output): iterable
    {
        $formatted = [];
        foreach ($output as $row) {
            $keyValue                      = explode('=', $row);
            $formatted[trim($keyValue[0])] = trim(implode('=', array_slice($keyValue, 1)));
        }
        return $formatted;
    }
}
