<?php

/**
 * This file is part of <PERSON><PERSON><PERSON><PERSON>\Git.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace <PERSON><PERSON><PERSON><PERSON>\Git\Command\Config;

use <PERSON><PERSON><PERSON><PERSON>\Git\Command\Base;

/**
 * Class Get
 *
 * @package <PERSON><PERSON><PERSON>mann\Git
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/sebastian<PERSON>mann/git
 * @since   Class available since Release 1.0.2
 */
class Get extends Base
{
    /**
     * The name of the configuration key to get
     *
     * @var string
     */
    private string $name;

    /**
     * The name of the configuration key to get.
     *
     * @param string $name
     * @return \SebastianF<PERSON>mann\Git\Command\Config\Get
     */
    public function name(string $name): Get
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Return the command to execute.
     *
     * @return string
     */
    protected function getGitCommand(): string
    {
        return 'config --get ' .  escapeshellarg($this->name);
    }
}
