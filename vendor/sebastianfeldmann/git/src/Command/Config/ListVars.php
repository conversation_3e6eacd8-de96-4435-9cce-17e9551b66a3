<?php

/**
 * This file is part of <PERSON><PERSON><PERSON><PERSON>\Git.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace <PERSON><PERSON><PERSON><PERSON>\Git\Command\Config;

use <PERSON><PERSON><PERSON><PERSON>\Git\Command\Base;

/**
 * Class ListVars
 *
 * @package <PERSON><PERSON><PERSON>mann\Git
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/sebastian<PERSON><PERSON>/git
 * @since   Class available since Release 3.14.0
 */
class ListVars extends Base
{
    /**
     * Return the command to execute.
     *
     * @return string
     */
    protected function getGitCommand(): string
    {
        return 'var -l';
    }
}
