<?php

/**
 * This file is part of <PERSON><PERSON><PERSON><PERSON>\Git.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace <PERSON><PERSON><PERSON><PERSON>\Git\Command\Config;

use <PERSON><PERSON><PERSON><PERSON>\Git\Command\Base;

/**
 * Class GetVar
 *
 * @package Sebastian<PERSON>eldmann\Git
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/sebastian<PERSON><PERSON>/git
 * @since   Class available since Release 3.14.0
 */
class GetVar extends Base
{
    /**
     * The name of the configuration key to get
     *
     * @var string
     */
    private string $name;

    /**
     * Set the name of the var to get
     *
     * @param string $name
     * @return \SebastianF<PERSON>mann\Git\Command\Config\GetVar
     */
    public function name(string $name): GetVar
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Return the `git var` command to execute
     *
     * @return string
     */
    protected function getGitCommand(): string
    {
        return 'var ' .  escapeshellarg($this->name);
    }
}
