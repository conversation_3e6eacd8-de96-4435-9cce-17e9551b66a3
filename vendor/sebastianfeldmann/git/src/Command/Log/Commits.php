<?php

/**
 * This file is part of <PERSON><PERSON><PERSON><PERSON>\Git.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace <PERSON><PERSON><PERSON><PERSON>\Git\Command\Log;

/**
 * Class Commits
 *
 * @package <PERSON><PERSON><PERSON><PERSON>\Git
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/sebastian<PERSON>mann/git
 * @since   Class available since Release 0.9.0
 */
class Commits extends Log
{
    /**
     * Return the command to execute.
     *
     * @return string
     * @throws \RuntimeException
     */
    protected function getGitCommand(): string
    {
        return 'log --pretty=' . $this->escape('format:' . $this->format)
               . $this->abbrev
               . $this->author
               . $this->merges
               . $this->date
               . $this->revSelection;
    }

    /**
     * This makes sure the % and ! signs within the format string will not be replaced on windows by 'escapeshellarg'
     *
     * @param  string $arg
     * @return string
     */
    private function escape(string $arg): string
    {
        // this is a dirty hack to make it work under windows
        return defined('PHP_WINDOWS_VERSION_MAJOR') ? '"' . $arg . '"' : escapeshellarg($arg);
    }
}
