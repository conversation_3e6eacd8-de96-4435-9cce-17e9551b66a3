<?php

/**
 * This file is part of Sebastian<PERSON><PERSON><PERSON>\Git.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace <PERSON><PERSON><PERSON><PERSON>\Git\Command\Log;

/**
 * Class ChangedFiles
 *
 * @package <PERSON><PERSON><PERSON>mann\Git
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/sebastian<PERSON>mann/git
 * @since   Class available since Release 0.9.0
 */
class ChangedFiles extends Log
{
    /**
     * Return the command to execute.
     *
     * @return string
     * @throws \RuntimeException
     */
    protected function getGitCommand(): string
    {
        return 'log --format=' . escapeshellarg('')
               . ' --name-only'
               . $this->diffFilter
               . $this->author
               . $this->merges
               . $this->date
               . $this->revSelection;
    }
}
