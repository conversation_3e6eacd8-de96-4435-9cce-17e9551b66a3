<?php

/**
 * This file is part of <PERSON><PERSON><PERSON><PERSON>\Git.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace <PERSON><PERSON><PERSON><PERSON>\Git\Command\Tag;

use <PERSON><PERSON><PERSON><PERSON>\Git\Command\Base;

/**
 * Class GetCurrentTag
 *
 * @package <PERSON><PERSON><PERSON>mann\Git
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/sebastian<PERSON>mann/git
 * @since   Class available since Release 2.3.0
 */
class GetTags extends Base
{
    /**
     * Commit to check for a tag
     *
     * @var string
     */
    private string $hash = 'HEAD';

    /**
     * Set the hash you want to check for tags, HEAD by default
     *
     * @param  string $hash
     * @return \SebastianF<PERSON>mann\Git\Command\Tag\GetTags
     */
    public function pointingTo(string $hash): GetTags
    {
        $this->hash = $hash;
        return $this;
    }

    /**
     * Return the command to execute.
     *
     * @return string
     */
    protected function getGitCommand(): string
    {
        return 'tag --points-at ' . escapeshellarg($this->hash);
    }
}
