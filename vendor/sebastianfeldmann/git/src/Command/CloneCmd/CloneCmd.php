<?php

/**
 * This file is part of <PERSON><PERSON><PERSON><PERSON>\Git.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\Git\Command\CloneCmd;

use <PERSON><PERSON><PERSON><PERSON>\Git\Command\Base;
use <PERSON><PERSON><PERSON><PERSON>\Git\Url;

/**
 * Class CloneCmd
 *
 * @package <PERSON><PERSON><PERSON><PERSON>\Git
 * <AUTHOR>
 * <AUTHOR> <<EMAIL>>
 * @link    https://github.com/sebastian<PERSON>mann/git
 * @since   Class available since Release 3.8.0
 */
final class CloneCmd extends Base
{
    /**
     * @var Url
     */
    private Url $url;

    /**
     * @var string
     */
    private string $dir = '';

    /**
     * @var string
     */
    private string $depth = '';

    public function __construct(Url $url)
    {
        $this->url = $url;
        parent::__construct();
    }

    /**
     * Specify the directory to clone into
     *
     * @param  string $dir
     * @return $this
     */
    public function dir(string $dir = ''): CloneCmd
    {
        $this->dir = $dir;
        return $this;
    }

    /**
     * Limit the history to the number of commits
     *
     * @param  int $depth
     * @return $this
     */
    public function depth(int $depth): CloneCmd
    {
        $this->depth = $this->useOption('--depth=' . $depth, true);
        return $this;
    }

    /**
     * Returns the git command to execute the clone
     *
     * @return string
     */
    protected function getGitCommand(): string
    {
        return 'clone'
            . $this->depth
            . ' '
            . escapeshellarg($this->url->getUrl())
            . $this->useOption(escapeshellarg($this->dir), !empty($this->dir));
    }
}
