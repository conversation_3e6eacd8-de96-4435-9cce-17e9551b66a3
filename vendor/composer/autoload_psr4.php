<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'phpDocumentor\\Reflection\\' => array($vendorDir . '/phpdocumentor/reflection-docblock/src', $vendorDir . '/phpdocumentor/type-resolver/src', $vendorDir . '/phpdocumentor/reflection-common/src'),
    'Whoops\\' => array($vendorDir . '/filp/whoops/src/Whoops'),
    'Webmozart\\Assert\\' => array($vendorDir . '/webmozart/assert/src'),
    'Termwind\\' => array($vendorDir . '/nunomaduro/termwind/src'),
    'Symfony\\Polyfill\\Php81\\' => array($vendorDir . '/symfony/polyfill-php81'),
    'Symfony\\Polyfill\\Php80\\' => array($vendorDir . '/symfony/polyfill-php80'),
    'Symfony\\Polyfill\\Php73\\' => array($vendorDir . '/symfony/polyfill-php73'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Intl\\Normalizer\\' => array($vendorDir . '/symfony/polyfill-intl-normalizer'),
    'Symfony\\Polyfill\\Intl\\Grapheme\\' => array($vendorDir . '/symfony/polyfill-intl-grapheme'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Symfony\\Contracts\\Service\\' => array($vendorDir . '/symfony/service-contracts'),
    'Symfony\\Contracts\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher-contracts'),
    'Symfony\\Component\\String\\' => array($vendorDir . '/symfony/string'),
    'Symfony\\Component\\Stopwatch\\' => array($vendorDir . '/symfony/stopwatch'),
    'Symfony\\Component\\Process\\' => array($vendorDir . '/symfony/process'),
    'Symfony\\Component\\OptionsResolver\\' => array($vendorDir . '/symfony/options-resolver'),
    'Symfony\\Component\\Finder\\' => array($vendorDir . '/symfony/finder'),
    'Symfony\\Component\\Filesystem\\' => array($vendorDir . '/symfony/filesystem'),
    'Symfony\\Component\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher'),
    'Symfony\\Component\\Console\\' => array($vendorDir . '/symfony/console'),
    'Seld\\Signal\\' => array($vendorDir . '/seld/signal-handler/src'),
    'Seld\\PharUtils\\' => array($vendorDir . '/seld/phar-utils/src'),
    'Seld\\JsonLint\\' => array($vendorDir . '/seld/jsonlint/src/Seld/JsonLint'),
    'SebastianFeldmann\\Git\\' => array($vendorDir . '/sebastianfeldmann/git/src'),
    'SebastianFeldmann\\Cli\\' => array($vendorDir . '/sebastianfeldmann/cli/src'),
    'SebastianFeldmann\\Camino\\' => array($vendorDir . '/sebastianfeldmann/camino/src'),
    'React\\Stream\\' => array($vendorDir . '/react/stream/src'),
    'React\\Socket\\' => array($vendorDir . '/react/socket/src'),
    'React\\Promise\\' => array($vendorDir . '/react/promise/src'),
    'React\\EventLoop\\' => array($vendorDir . '/react/event-loop/src'),
    'React\\Dns\\' => array($vendorDir . '/react/dns/src'),
    'React\\ChildProcess\\' => array($vendorDir . '/react/child-process/src'),
    'React\\Cache\\' => array($vendorDir . '/react/cache/src'),
    'Ramsey\\ConventionalCommits\\' => array($vendorDir . '/ramsey/conventional-commits/src/ConventionalCommits'),
    'Ramsey\\CaptainHook\\' => array($vendorDir . '/ramsey/conventional-commits/src/CaptainHook'),
    'Psr\\SimpleCache\\' => array($vendorDir . '/psr/simple-cache/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/src'),
    'Psr\\EventDispatcher\\' => array($vendorDir . '/psr/event-dispatcher/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'PhpParser\\' => array($vendorDir . '/nikic/php-parser/lib/PhpParser'),
    'PhpCsFixer\\' => array($vendorDir . '/friendsofphp/php-cs-fixer/src'),
    'Pest\\Plugin\\' => array($vendorDir . '/pestphp/pest-plugin/src'),
    'Pest\\Mutate\\' => array($vendorDir . '/pestphp/pest-plugin-mutate/src'),
    'Pest\\Arch\\' => array($vendorDir . '/pestphp/pest-plugin-arch/src'),
    'Pest\\' => array($vendorDir . '/pestphp/pest/src'),
    'ParaTest\\' => array($vendorDir . '/brianium/paratest/src'),
    'PHPUnit\\Architecture\\' => array($vendorDir . '/ta-tikoma/phpunit-architecture-test/src'),
    'PHPStan\\PhpDocParser\\' => array($vendorDir . '/phpstan/phpdoc-parser/src'),
    'Opis\\Uri\\' => array($vendorDir . '/opis/uri/src'),
    'Opis\\String\\' => array($vendorDir . '/opis/string/src'),
    'Opis\\JsonSchema\\' => array($vendorDir . '/opis/json-schema/src'),
    'NunoMaduro\\Collision\\' => array($vendorDir . '/nunomaduro/collision/src'),
    'MabeEnum\\' => array($vendorDir . '/marc-mabe/php-enum/src'),
    'JsonSchema\\' => array($vendorDir . '/justinrainbow/json-schema/src/JsonSchema'),
    'Jean85\\' => array($vendorDir . '/jean85/pretty-package-versions/src'),
    'Jawira\\CaseConverter\\' => array($vendorDir . '/jawira/case-converter/src'),
    'Fidry\\CpuCoreCounter\\' => array($vendorDir . '/fidry/cpu-core-counter/src'),
    'Evenement\\' => array($vendorDir . '/evenement/evenement/src'),
    'Doctrine\\Deprecations\\' => array($vendorDir . '/doctrine/deprecations/src'),
    'DeepCopy\\' => array($vendorDir . '/myclabs/deep-copy/src/DeepCopy'),
    'Composer\\XdebugHandler\\' => array($vendorDir . '/composer/xdebug-handler/src'),
    'Composer\\Spdx\\' => array($vendorDir . '/composer/spdx-licenses/src'),
    'Composer\\Semver\\' => array($vendorDir . '/composer/semver/src'),
    'Composer\\Pcre\\' => array($vendorDir . '/composer/pcre/src'),
    'Composer\\MetadataMinifier\\' => array($vendorDir . '/composer/metadata-minifier/src'),
    'Composer\\ClassMapGenerator\\' => array($vendorDir . '/composer/class-map-generator/src'),
    'Composer\\CaBundle\\' => array($vendorDir . '/composer/ca-bundle/src'),
    'Composer\\' => array($vendorDir . '/composer/composer/src/Composer'),
    'Clue\\React\\NDJson\\' => array($vendorDir . '/clue/ndjson-react/src'),
    'CaptainHook\\Secrets\\' => array($vendorDir . '/captainhook/secrets/src'),
    'CaptainHook\\HookInstaller\\' => array($vendorDir . '/captainhook/hook-installer/src'),
    'CaptainHook\\App\\' => array($vendorDir . '/captainhook/captainhook/src'),
    'App\\Tests\\' => array($baseDir . '/tests'),
    'App\\' => array($baseDir . '/app'),
);
