<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'brianium/paratest' => array(
            'pretty_version' => 'v7.8.3',
            'version' => '7.8.3.0',
            'reference' => 'a585c346ddf1bec22e51e20b5387607905604a71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brianium/paratest',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'captainhook/captainhook' => array(
            'pretty_version' => '5.25.5',
            'version' => '5.25.5.0',
            'reference' => '7b0feb07ae328c83c458175fe19e5cb55634b1fb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../captainhook/captainhook',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'captainhook/hook-installer' => array(
            'pretty_version' => '1.0.4',
            'version' => '1.0.4.0',
            'reference' => 'fb3c45f6204b08baba999f4ffc4ae707bf684e8b',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../captainhook/hook-installer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'captainhook/secrets' => array(
            'pretty_version' => '0.9.7',
            'version' => '0.9.7.0',
            'reference' => 'd62c97f75f81ac98e22f1c282482bd35fa82f631',
            'type' => 'library',
            'install_path' => __DIR__ . '/../captainhook/secrets',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'clue/ndjson-react' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => '392dc165fce93b5bb5c637b67e59619223c931b0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../clue/ndjson-react',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/ca-bundle' => array(
            'pretty_version' => '1.5.7',
            'version' => '1.5.7.0',
            'reference' => 'd665d22c417056996c59019579f1967dfe5c1e82',
            'type' => 'library',
            'install_path' => __DIR__ . '/./ca-bundle',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/class-map-generator' => array(
            'pretty_version' => '1.6.1',
            'version' => '1.6.1.0',
            'reference' => '134b705ddb0025d397d8318a75825fe3c9d1da34',
            'type' => 'library',
            'install_path' => __DIR__ . '/./class-map-generator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/composer' => array(
            'pretty_version' => '2.8.9',
            'version' => '2.8.9.0',
            'reference' => 'b4e6bff2db7ce756ddb77ecee958a0f41f42bd9d',
            'type' => 'library',
            'install_path' => __DIR__ . '/./composer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/metadata-minifier' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'c549d23829536f0d0e984aaabbf02af91f443207',
            'type' => 'library',
            'install_path' => __DIR__ . '/./metadata-minifier',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/pcre' => array(
            'pretty_version' => '3.3.2',
            'version' => '3.3.2.0',
            'reference' => 'b2bed4734f0cc156ee1fe9c0da2550420d99a21e',
            'type' => 'library',
            'install_path' => __DIR__ . '/./pcre',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/semver' => array(
            'pretty_version' => '3.4.3',
            'version' => '3.4.3.0',
            'reference' => '4313d26ada5e0c4edfbd1dc481a92ff7bff91f12',
            'type' => 'library',
            'install_path' => __DIR__ . '/./semver',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/spdx-licenses' => array(
            'pretty_version' => '1.5.9',
            'version' => '1.5.9.0',
            'reference' => 'edf364cefe8c43501e21e88110aac10b284c3c9f',
            'type' => 'library',
            'install_path' => __DIR__ . '/./spdx-licenses',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/xdebug-handler' => array(
            'pretty_version' => '3.0.5',
            'version' => '3.0.5.0',
            'reference' => '6c1925561632e83d60a44492e0b344cf48ab85ef',
            'type' => 'library',
            'install_path' => __DIR__ . '/./xdebug-handler',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'doctrine/deprecations' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/deprecations',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'evenement/evenement' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => '0a16b0d71ab13284339abb99d9d2bd813640efbc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../evenement/evenement',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'fidry/cpu-core-counter' => array(
            'pretty_version' => '1.2.0',
            'version' => '1.2.0.0',
            'reference' => '8520451a140d3f46ac33042715115e290cf5785f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fidry/cpu-core-counter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'filp/whoops' => array(
            'pretty_version' => '2.18.3',
            'version' => '2.18.3.0',
            'reference' => '59a123a3d459c5a23055802237cb317f609867e5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filp/whoops',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'friendsofphp/php-cs-fixer' => array(
            'pretty_version' => 'v3.75.0',
            'version' => '3.75.0.0',
            'reference' => '399a128ff2fdaf4281e4e79b755693286cdf325c',
            'type' => 'application',
            'install_path' => __DIR__ . '/../friendsofphp/php-cs-fixer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'jawira/case-converter' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => 'de9956122568743a83e0fc7e2eaa92c1b0de3f18',
            'type' => 'library',
            'install_path' => __DIR__ . '/../jawira/case-converter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'jean85/pretty-package-versions' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '4d7aa5dab42e2a76d99559706022885de0e18e1a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../jean85/pretty-package-versions',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'justinrainbow/json-schema' => array(
            'pretty_version' => '6.4.2',
            'version' => '6.4.2.0',
            'reference' => 'ce1fd2d47799bb60668643bc6220f6278a4c1d02',
            'type' => 'library',
            'install_path' => __DIR__ . '/../justinrainbow/json-schema',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'marc-mabe/php-enum' => array(
            'pretty_version' => 'v4.7.1',
            'version' => '4.7.1.0',
            'reference' => '7159809e5cfa041dca28e61f7f7ae58063aae8ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../marc-mabe/php-enum',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.13.1',
            'version' => '1.13.1.0',
            'reference' => '1720ddd719e16cf0db4eb1c6eca108031636d46c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v5.5.0',
            'version' => '5.5.0.0',
            'reference' => 'ae59794362fe85e051a58ad36b289443f57be7a9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nunomaduro/collision' => array(
            'pretty_version' => 'v8.8.2',
            'version' => '8.8.2.0',
            'reference' => '60207965f9b7b7a4ce15a0f75d57f9dadb105bdb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/collision',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nunomaduro/termwind' => array(
            'pretty_version' => 'v2.3.1',
            'version' => '2.3.1.0',
            'reference' => 'dfa08f390e509967a15c22493dc0bac5733d9123',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/termwind',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'opis/json-schema' => array(
            'pretty_version' => '2.4.1',
            'version' => '2.4.1.0',
            'reference' => '712827751c62b465daae6e725bf0cf5ffbf965e1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../opis/json-schema',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'opis/string' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'ba0b9607b9809462b0e28a11e4881a8d77431feb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../opis/string',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'opis/uri' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '0f3ca49ab1a5e4a6681c286e0b2cc081b93a7d5a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../opis/uri',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'pestphp/pest' => array(
            'pretty_version' => 'v3.8.2',
            'version' => '3.8.2.0',
            'reference' => 'c6244a8712968dbac88eb998e7ff3b5caa556b0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pestphp/pest',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'pestphp/pest-plugin' => array(
            'pretty_version' => 'v3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'e79b26c65bc11c41093b10150c1341cc5cdbea83',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../pestphp/pest-plugin',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'pestphp/pest-plugin-arch' => array(
            'pretty_version' => 'v3.1.1',
            'version' => '3.1.1.0',
            'reference' => 'db7bd9cb1612b223e16618d85475c6f63b9c8daa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pestphp/pest-plugin-arch',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'pestphp/pest-plugin-mutate' => array(
            'pretty_version' => 'v3.0.5',
            'version' => '3.0.5.0',
            'reference' => 'e10dbdc98c9e2f3890095b4fe2144f63a5717e08',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pestphp/pest-plugin-mutate',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phar-io/manifest' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '54750ef60c58e43759730615a392c31c80e23176',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/manifest',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phar-io/version' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '4f7fd7836c6f332bb2933569e566a0d6c4cbed74',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpdocumentor/reflection-common' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '1d01c49d4ed62f25aa84a747ad35d5a16924662b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/reflection-common',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpdocumentor/reflection-docblock' => array(
            'pretty_version' => '5.6.2',
            'version' => '5.6.2.0',
            'reference' => '92dde6a5919e34835c506ac8c523ef095a95ed62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/reflection-docblock',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpdocumentor/type-resolver' => array(
            'pretty_version' => '1.10.0',
            'version' => '1.10.0.0',
            'reference' => '679e3ce485b99e84c775d28e2e96fade9a7fb50a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/type-resolver',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpstan/phpdoc-parser' => array(
            'pretty_version' => '2.1.0',
            'version' => '2.1.0.0',
            'reference' => '9b30d6fd026b2c132b3985ce6b23bec09ab3aa68',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpstan/phpdoc-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpstan/phpstan' => array(
            'pretty_version' => '2.1.17',
            'version' => '2.1.17.0',
            'reference' => '89b5ef665716fa2a52ecd2633f21007a6a349053',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpstan/phpstan',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-code-coverage' => array(
            'pretty_version' => '11.0.10',
            'version' => '11.0.10.0',
            'reference' => '1a800a7446add2d79cc6b3c01c45381810367d76',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-code-coverage',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-file-iterator' => array(
            'pretty_version' => '5.1.0',
            'version' => '5.1.0.0',
            'reference' => '118cfaaa8bc5aef3287bf315b6060b1174754af6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-file-iterator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-invoker' => array(
            'pretty_version' => '5.0.1',
            'version' => '5.0.1.0',
            'reference' => 'c1ca3814734c07492b3d4c5f794f4b0995333da2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-invoker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-text-template' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => '3e0404dc6b300e6bf56415467ebcb3fe4f33e964',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-text-template',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-timer' => array(
            'pretty_version' => '7.0.1',
            'version' => '7.0.1.0',
            'reference' => '3b415def83fbcb41f991d9ebf16ae4ad8b7837b3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-timer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/phpunit' => array(
            'pretty_version' => '11.5.15',
            'version' => '11.5.15.0',
            'reference' => '4b6a4ee654e5e0c5e1f17e2f83c0f4c91dee1f9c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'ramsey/conventional-commits' => array(
            'pretty_version' => '1.6.0',
            'version' => '1.6.0.0',
            'reference' => '3eb46b9046d91f5b35462ff770a9d0326601783d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/conventional-commits',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/cache' => array(
            'pretty_version' => 'v1.2.0',
            'version' => '1.2.0.0',
            'reference' => 'd47c472b64aa5608225f47965a484b75c7817d5b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/cache',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/child-process' => array(
            'pretty_version' => 'v0.6.6',
            'version' => '0.6.6.0',
            'reference' => '1721e2b93d89b745664353b9cfc8f155ba8a6159',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/child-process',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/dns' => array(
            'pretty_version' => 'v1.13.0',
            'version' => '1.13.0.0',
            'reference' => 'eb8ae001b5a455665c89c1df97f6fb682f8fb0f5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/dns',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/event-loop' => array(
            'pretty_version' => 'v1.5.0',
            'version' => '1.5.0.0',
            'reference' => 'bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/event-loop',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/promise' => array(
            'pretty_version' => 'v3.2.0',
            'version' => '3.2.0.0',
            'reference' => '8a164643313c71354582dc850b42b33fa12a4b63',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/promise',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/socket' => array(
            'pretty_version' => 'v1.16.0',
            'version' => '1.16.0.0',
            'reference' => '23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/socket',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/stream' => array(
            'pretty_version' => 'v1.4.0',
            'version' => '1.4.0.0',
            'reference' => '1e5b0acb8fe55143b5b426817155190eb6f5b18d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/stream',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/cli-parser' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => '15c5dd40dc4f38794d383bb95465193f5e0ae180',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/cli-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '54391c61e4af8078e5b276ab082b6d3c54c9ad64',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit-reverse-lookup' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => '183a9b2632194febd219bb9246eee421dad8d45e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit-reverse-lookup',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/comparator' => array(
            'pretty_version' => '6.3.1',
            'version' => '6.3.1.0',
            'reference' => '24b8fbc2c8e201bb1308e7b05148d6ab393b6959',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/comparator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/complexity' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => 'ee41d384ab1906c68852636b6de493846e13e5a0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/complexity',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '6.0.2',
            'version' => '6.0.2.0',
            'reference' => 'b4ccd857127db5d41a5b676f24b51371d76d8544',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/environment' => array(
            'pretty_version' => '7.2.1',
            'version' => '7.2.1.0',
            'reference' => 'a5c75038693ad2e8d4b6c15ba2403532647830c4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/environment',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/exporter' => array(
            'pretty_version' => '6.3.0',
            'version' => '6.3.0.0',
            'reference' => '3473f61172093b2da7de1fb5782e1f24cc036dc3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/exporter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/global-state' => array(
            'pretty_version' => '7.0.2',
            'version' => '7.0.2.0',
            'reference' => '3be331570a721f9a4b5917f4209773de17f747d7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/global-state',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/lines-of-code' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => 'd36ad0d782e5756913e42ad87cb2890f4ffe467a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/lines-of-code',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-enumerator' => array(
            'pretty_version' => '6.0.1',
            'version' => '6.0.1.0',
            'reference' => 'f5b498e631a74204185071eb41f33f38d64608aa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-enumerator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-reflector' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => '6e1a43b411b2ad34146dee7524cb13a068bb35f9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-reflector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/recursion-context' => array(
            'pretty_version' => '6.0.2',
            'version' => '6.0.2.0',
            'reference' => '694d156164372abbd149a4b85ccda2e4670c0e16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/recursion-context',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/type' => array(
            'pretty_version' => '5.1.2',
            'version' => '5.1.2.0',
            'reference' => 'a8a7e30534b0eb0c77cd9d07e82de1a114389f5e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/type',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/version' => array(
            'pretty_version' => '5.0.2',
            'version' => '5.0.2.0',
            'reference' => 'c687e3387b99f5b03b6caa64c74b63e2936ff874',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastianfeldmann/camino' => array(
            'pretty_version' => '0.9.5',
            'version' => '0.9.5.0',
            'reference' => 'bf2e4c8b2a029e9eade43666132b61331e3e8184',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastianfeldmann/camino',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastianfeldmann/captainhook' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'sebastianfeldmann/cli' => array(
            'pretty_version' => '3.4.2',
            'version' => '3.4.2.0',
            'reference' => '6fa122afd528dae7d7ec988a604aa6c600f5d9b5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastianfeldmann/cli',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastianfeldmann/git' => array(
            'pretty_version' => '3.14.3',
            'version' => '3.14.3.0',
            'reference' => '22584df8df01d95b0700000cfd855779fae7d8ea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastianfeldmann/git',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'seld/jsonlint' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '1748aaf847fc731cfad7725aec413ee46f0cc3a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../seld/jsonlint',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'seld/phar-utils' => array(
            'pretty_version' => '1.2.1',
            'version' => '1.2.1.0',
            'reference' => 'ea2f4014f163c1be4c601b9b7bd6af81ba8d701c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../seld/phar-utils',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'seld/signal-handler' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => '04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98',
            'type' => 'library',
            'install_path' => __DIR__ . '/../seld/signal-handler',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'staabm/side-effects-detector' => array(
            'pretty_version' => '1.0.5',
            'version' => '1.0.5.0',
            'reference' => 'd8334211a140ce329c13726d4a715adbddd0a163',
            'type' => 'library',
            'install_path' => __DIR__ . '/../staabm/side-effects-detector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'reference' => '9e27aecde8f506ba0fd1d9989620c04a87697101',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '63afe740e99a13ba87ec199bb07bbdee937a5b62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '497f73ac996a598c92409b44ac43b6690c4f666d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '59eb412e93815df44f05f342958efa9f46b1e586',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/filesystem' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'b8dce482de9d7c9fe2891155035a7248ab5c7fdb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/filesystem',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'ec2344cf77a48253bbca6939aa3d2477773ea63d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/options-resolver' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'afb9a8038025e5dbc657378bfab9198d75f10fca',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/options-resolver',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-php73' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '0f68c03565dcaaf25a890667542e8bd75fe7e5bb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php73',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '0cc9dd0f17f61d8131e7df6b84bd344899fe2608',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-php81' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php81',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '40c295f2deb408d5e9d2d32b8ba1dd61e36f05af',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => 'f021b05a130d35510bd6b25fe9053c2a8a15d5d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/stopwatch' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/stopwatch',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'f3570b8c61ca887a9e2938e85cb6458515d2b125',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'ta-tikoma/phpunit-architecture-test' => array(
            'pretty_version' => '0.8.5',
            'version' => '0.8.5.0',
            'reference' => 'cf6fb197b676ba716837c886baca842e4db29005',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ta-tikoma/phpunit-architecture-test',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'theseer/tokenizer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'reference' => '737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theseer/tokenizer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
    ),
);
