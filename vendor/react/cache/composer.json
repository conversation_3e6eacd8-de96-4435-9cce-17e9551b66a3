{"name": "react/cache", "description": "Async, Promise-based cache interface for ReactPHP", "keywords": ["cache", "caching", "promise", "ReactPHP"], "license": "MIT", "authors": [{"name": "<PERSON>", "homepage": "https://clue.engineering/", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://wyrihaximus.net/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://sorgalla.com/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://cboden.dev/", "email": "<EMAIL>"}], "require": {"php": ">=5.3.0", "react/promise": "^3.0 || ^2.0 || ^1.1"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^5.7 || ^4.8.35"}, "autoload": {"psr-4": {"React\\Cache\\": "src/"}}, "autoload-dev": {"psr-4": {"React\\Tests\\Cache\\": "tests/"}}}