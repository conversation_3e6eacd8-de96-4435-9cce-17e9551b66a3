{"name": "react/child-process", "description": "Event-driven library for executing child processes with ReactPHP.", "keywords": ["process", "event-driven", "ReactPHP"], "license": "MIT", "authors": [{"name": "<PERSON>", "homepage": "https://clue.engineering/", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://wyrihaximus.net/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://sorgalla.com/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://cboden.dev/", "email": "<EMAIL>"}], "require": {"php": ">=5.3.0", "evenement/evenement": "^3.0 || ^2.0 || ^1.0", "react/event-loop": "^1.2", "react/stream": "^1.4"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/socket": "^1.16", "sebastian/environment": "^5.0 || ^3.0 || ^2.0 || ^1.0"}, "autoload": {"psr-4": {"React\\ChildProcess\\": "src/"}}, "autoload-dev": {"psr-4": {"React\\Tests\\ChildProcess\\": "tests/"}}}